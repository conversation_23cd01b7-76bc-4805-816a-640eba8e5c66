#!/usr/bin/env python3
"""
Independent DNS server for VR360 service discovery
"""

import socket
import time
import sys

# DNS broadcast configuration
BROADCAST_PORT = 50000
SERVER_PORT = 6000

def get_local_ip():
    """Get local IP address"""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(("*******", 80))
    ip = s.getsockname()[0]
    s.close()
    return ip

def start_dns_server():
    """Start UDP broadcast server for service discovery"""
    try:
        server_ip = get_local_ip()
        print(f"[DNS] UDP discovery server running on {server_ip}:{BROADCAST_PORT}")

        # Set up UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(("", BROADCAST_PORT))

        print(f"[DNS] UDP socket bound successfully on port {BROADCAST_PORT}")

        while True:
            try:
                data, addr = sock.recvfrom(1024)
                print(f"[DNS] Received broadcast from {addr}: {data.decode()}")
                if data.decode() == "DISCOVER_SERVER":
                    response = f"{server_ip}:{SERVER_PORT}"
                    sock.sendto(response.encode(), addr)
                    print(f"[DNS] Responded to {addr} with server address: {response}")
            except Exception as e:
                print(f"[DNS] Error in UDP loop: {e}")
                time.sleep(0.1)
                continue
                
    except KeyboardInterrupt:
        print("\n[DNS] DNS server stopped")
    except Exception as e:
        print(f"[DNS] Error in DNS server: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    start_dns_server()
