import asyncio
import json
import logging
import time
import datetime
import os
import glob
import socket
import subprocess
import platform
import re
from aiohttp import web
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack, RTCConfiguration, RTCIceServer
from aiortc.contrib.media import MediaRelay
import zmq
import zmq.asyncio
import base64
from PIL import Image
import io
import cv2
import numpy as np

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

server_logger = logging.getLogger('SERVER')
zmq_logger = logging.getLogger('ZMQ')
client_logger = logging.getLogger('CLIENT')

class ZMQDataManager:
    """ZMQ data manager for receiving panorama, segmentation and GPS data"""
    def __init__(self):
        self.context = zmq.asyncio.Context()
        self.panorama_receiver = None
        self.segmentation_receiver = None
        self.gps_receiver = None
        
        self.panorama_data = None
        self.segmentation_data = None
        self.gps_data = None
        
        self.panorama_processed = None
        self.segmentation_processed = None
        
        self.panorama_timestamp = 0
        self.segmentation_timestamp = 0
        self.gps_timestamp = 0
        
        self.running = False
        self.zmq_port = 5555
        
    def _setup_receiver(self, topic_name, timeout=100):
        """Setup a single ZMQ receiver"""
        receiver = self.context.socket(zmq.SUB)
        receiver.connect(f"tcp://localhost:{self.zmq_port}")
        receiver.setsockopt(zmq.SUBSCRIBE, topic_name.encode())
        receiver.setsockopt(zmq.RCVTIMEO, timeout)
        return receiver
    
    async def setup_receivers(self):
        """Setup ZMQ receivers"""
        try:
            self.panorama_receiver = self._setup_receiver("panorama")
            self.segmentation_receiver = self._setup_receiver("segmentation")
            self.gps_receiver = self._setup_receiver("gps")
            
            zmq_logger.info(f"ZMQ receivers setup complete on port {self.zmq_port}")
            
        except Exception as e:
            zmq_logger.error(f"Error setting up receivers: {e}")
            
    async def start_receivers(self):
        """Start receiver threads"""
        self.running = True
        asyncio.create_task(self._receive_data_loop())
        zmq_logger.info("Data receiver started")
        
    async def _receive_data_loop(self):
        """Main loop to receive all types of data"""
        while self.running:
            try:
                # Panorama data
                if self.panorama_receiver:
                    try:
                        topic, data = await self.panorama_receiver.recv_multipart(zmq.NOBLOCK)
                        if topic == b"panorama":
                            self.panorama_data = data
                            self.panorama_processed = data.decode('utf-8')
                            self.panorama_timestamp = time.time()
                    except zmq.Again:
                        pass
                
                # Segmentation data
                if self.segmentation_receiver:
                    try:
                        topic, data = await self.segmentation_receiver.recv_multipart(zmq.NOBLOCK)
                        if topic == b"segmentation":
                            self.segmentation_data = data
                            self.segmentation_processed = data.decode('utf-8')
                            self.segmentation_timestamp = time.time()
                    except zmq.Again:
                        pass
                
                # GPS data
                if self.gps_receiver:
                    try:
                        topic, data = await self.gps_receiver.recv_multipart(zmq.NOBLOCK)
                        if topic == b"gps":
                            self.gps_data = json.loads(data.decode('utf-8'))
                            self.gps_timestamp = time.time()
                    except zmq.Again:
                        pass
                    
                await asyncio.sleep(0.01)
                
            except Exception as e:
                zmq_logger.error(f"Error in data receive loop: {e}")
                await asyncio.sleep(0.1)
                
    def get_image_data(self, mode='panorama'):
        """Get processed image data with timestamp"""
        if mode == 'panorama':
            return self.panorama_processed, self.panorama_timestamp
        elif mode == 'segmentation':
            return self.segmentation_processed, self.segmentation_timestamp
        return None, 0
            
    def get_gps_data(self):
        """Get GPS data with timestamp"""
        if self.gps_data:
            return self.gps_data.copy(), self.gps_timestamp
        return None, 0
            
    async def stop(self):
        """Stop all receivers"""
        self.running = False
        if self.panorama_receiver:
            self.panorama_receiver.close()
        if self.segmentation_receiver:
            self.segmentation_receiver.close()
        if self.gps_receiver:
            self.gps_receiver.close()
        self.context.term()
        zmq_logger.info("All receivers stopped")

class CustomVideoStreamTrack(VideoStreamTrack):
    """Custom video track that sends image data from ZMQ"""
    def __init__(self, zmq_manager, client_id, mode='panorama'):
        super().__init__()
        self.zmq_manager = zmq_manager
        self.client_id = client_id
        self.mode = mode
        self.last_timestamp = 0
        self.cached_frame_data = None
        
    def _create_frame_with_pts(self, frame, pts, time_base):
        """Create VideoFrame with timestamp"""
        from av import VideoFrame
        av_frame = VideoFrame.from_ndarray(frame, format="bgr24")
        av_frame.pts = pts
        av_frame.time_base = time_base
        return av_frame
    
    def _get_frame_to_send(self, pts, time_base, b64_image, current_timestamp):
        """Determine which frame to send based on data availability and timestamp"""
        # No data available
        if b64_image is None:
            if self.cached_frame_data is not None:
                return self._create_frame_with_pts(self.cached_frame_data, pts, time_base)
            else:
                frame = np.zeros((480, 640, 3), dtype=np.uint8)
                return self._create_frame_with_pts(frame, pts, time_base)

        # Same timestamp - return cached frame
        if current_timestamp == self.last_timestamp and self.cached_frame_data is not None:
            return self._create_frame_with_pts(self.cached_frame_data, pts, time_base)
        
        # New data - decode and process
        try:
            self.last_timestamp = current_timestamp
            
            img_bytes = base64.b64decode(b64_image)
            image = Image.open(io.BytesIO(img_bytes))
            frame = np.array(image)
            
            # Ensure correct format
            if len(frame.shape) == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
            elif len(frame.shape) == 3 and frame.shape[2] == 3:
                frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            elif len(frame.shape) == 3 and frame.shape[2] == 4:
                frame = cv2.cvtColor(frame, cv2.COLOR_RGBA2BGR)
            
            if frame.dtype != np.uint8:
                frame = frame.astype(np.uint8)
            
            self.cached_frame_data = frame.copy()
            return self._create_frame_with_pts(frame, pts, time_base)
            
        except Exception as e:
            server_logger.error(f"Error processing new frame: {e}")
            # Fallback to cached or black frame
            fallback_frame = self.cached_frame_data if self.cached_frame_data is not None else np.zeros((480, 640, 3), dtype=np.uint8)
            return self._create_frame_with_pts(fallback_frame, pts, time_base)

    async def recv(self):
        """Generate video frames from ZMQ data"""
        try:
            pts, time_base = await self.next_timestamp()
            b64_image, current_timestamp = self.zmq_manager.get_image_data(self.mode)
            av_frame = self._get_frame_to_send(pts, time_base, b64_image, current_timestamp)
            return av_frame
            
        except Exception as e:
            server_logger.error(f"Error in video track recv: {e}")
            pts, time_base = await self.next_timestamp()
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            return self._create_frame_with_pts(frame, pts, time_base)

class WebRTCManager:
    """WebRTC connection management for multiple clients"""
    def __init__(self, zmq_manager):
        self.zmq_manager = zmq_manager
        self.peer_connections = {}
        self.client_modes = {}
        self.video_tracks = {}
        self.data_channels = {}
        
        self.video_streaming = {}
        self.gps_streaming = {}
        self.gps_tasks = {}
        
        self.client_last_panorama_timestamp = {}
        self.client_last_segmentation_timestamp = {}
        self.client_last_gps_timestamp = {}
        
        self.relay = MediaRelay()
        
        # RTT monitoring
        self.rtt_monitoring_task = None
        self.rtt_monitoring_active = False
    
    async def create_peer_connection(self, client_id):
        """Create a new peer connection for a client"""
        config = RTCConfiguration(
            iceServers=[
                RTCIceServer(urls=["stun:stun.l.google.com:19302"])
            ]
        )
        
        pc = RTCPeerConnection(configuration=config)
        self.peer_connections[client_id] = pc
        self.client_modes[client_id] = 'panorama'
        
        video_track = CustomVideoStreamTrack(self.zmq_manager, client_id, self.client_modes.get(client_id, 'panorama'))
        pc.addTrack(video_track)
        self.video_tracks[client_id] = video_track
        self.video_streaming[client_id] = True
        
        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            if pc.connectionState in ["connected", "failed", "closed"]:
                client_logger.info(f"Client {client_id} connection: {pc.connectionState}")
            if pc.connectionState == "closed":
                await self.remove_client(client_id)
        
        @pc.on("datachannel")
        def on_datachannel(channel):
            client_logger.info(f"Data channel established for {client_id}")
            self.data_channels[client_id] = channel
            
            @channel.on("open")
            def on_open():
                client_logger.info(f"Data channel ready for {client_id}")
            
            @channel.on("message")
            def on_message(message):
                asyncio.create_task(self.handle_data_channel_message(client_id, channel, message))
        
        client_logger.info(f"New client connected: {client_id}")
        
        # Start RTT monitoring if this is the first client
        if len(self.peer_connections) == 1 and not self.rtt_monitoring_active:
            await self.start_rtt_monitoring()
        
        return pc
    
    async def handle_data_channel_message(self, client_id, channel, message):
        """Handle messages from data channel"""
        try:
            # Parse message in [topic, content] format
            pattern = r'^\[\s*([^,\]]+)\s*,\s*(.*?)\s*\]$'
            match = re.match(pattern, message.strip())
            
            if match:
                topic = match.group(1).strip()
                content = match.group(2).strip()
                
                if topic == 'set_mode':
                    mode = content if content else 'panorama'
                    await self.set_client_mode(client_id, mode)
                    
                elif topic == 'start_video':
                    mode = content if content else 'panorama'
                    await self.start_video_stream(client_id, mode)
                    
                elif topic == 'stop_video':
                    await self.stop_video_stream(client_id)
                    
                elif topic == 'start_gps':
                    await self.start_gps_stream_for_client(client_id)
                    
                elif topic == 'stop_gps':
                    await self.stop_gps_stream_for_client(client_id)
                    
                else:
                    client_logger.warning(f"Unknown topic '{topic}' from {client_id}")
            else:
                client_logger.warning(f"Invalid message format from {client_id}: {message[:50]}...")
                
        except Exception as e:
            client_logger.error(f"Error handling data channel message from {client_id}: {e}")
    
    async def set_client_mode(self, client_id, mode):
        """Set client mode (panorama/segmentation)"""
        self.client_modes[client_id] = mode
        
        # Update video track mode
        if client_id in self.video_tracks:
            self.video_tracks[client_id].mode = mode
            
        client_logger.info(f"Client {client_id} switched to {mode} mode")

    async def start_video_stream(self, client_id, mode):
        """Start video streaming for a client"""
        if client_id in self.peer_connections:
            self.video_streaming[client_id] = True
            self.set_client_mode(client_id, mode)
            client_logger.info(f"Video stream started for {client_id} ({mode})")
        else:
            client_logger.error(f"Cannot start video stream - no connection for {client_id}")
    
    async def stop_video_stream(self, client_id):
        """Stop video streaming for a client"""
        if client_id in self.video_streaming and self.video_streaming[client_id]:
            if client_id in self.peer_connections and client_id in self.video_tracks:
                pc = self.peer_connections[client_id]
                video_track = self.video_tracks[client_id]
                
                for sender in pc.getSenders():
                    if sender.track == video_track:
                        pc.removeTrack(sender)
                        break
                
                del self.video_tracks[client_id]
                self.video_streaming[client_id] = False
                
                client_logger.info(f"Video stream stopped for {client_id}")
            else:
                client_logger.error(f"Cannot stop video stream - no track found for {client_id}")
    
    async def start_gps_stream_for_client(self, client_id):
        """Start GPS streaming for a specific client"""
        if client_id not in self.gps_streaming or not self.gps_streaming[client_id]:
            if client_id in self.data_channels:
                channel = self.data_channels[client_id]
                
                # Start GPS streaming task
                task = asyncio.create_task(self._gps_stream_loop(client_id, channel))
                self.gps_tasks[client_id] = task
                self.gps_streaming[client_id] = True
                
                client_logger.info(f"GPS stream started for {client_id}")
            else:
                client_logger.error(f"Cannot start GPS stream - no data channel for {client_id}")
    
    async def stop_gps_stream_for_client(self, client_id):
        """Stop GPS streaming for a specific client"""
        if client_id in self.gps_streaming and self.gps_streaming[client_id]:
            # Cancel GPS streaming task
            if client_id in self.gps_tasks:
                task = self.gps_tasks[client_id]
                task.cancel()
                del self.gps_tasks[client_id]
            
            self.gps_streaming[client_id] = False
            client_logger.info(f"GPS stream stopped for {client_id}")
    
    async def _gps_stream_loop(self, client_id, channel):
        """GPS streaming loop for a specific client"""
        while self.gps_streaming.get(client_id, False) and channel.readyState == "open":
            try:
                gps_data, current_timestamp = self.zmq_manager.get_gps_data()
                if gps_data is not None and self.should_send_gps(client_id, current_timestamp):
                    gps_content = str(gps_data) if gps_data else ""
                    message = f"[gps, {gps_content}]"
                    channel.send(message)
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                client_logger.error(f"Error in GPS stream loop for {client_id}: {e}")
                break
    
    def should_send_gps(self, client_id, current_timestamp):
        """Check if GPS data should be sent based on timestamp"""
        last_timestamp = self.client_last_gps_timestamp.get(client_id, 0)
        
        if current_timestamp != last_timestamp:
            self.client_last_gps_timestamp[client_id] = current_timestamp
            return True
        return False
    
    async def remove_client(self, client_id):
        """Remove a client and clean up all associated data"""
        if client_id in self.peer_connections:
            pc = self.peer_connections[client_id]
            await pc.close()
            del self.peer_connections[client_id]
        
        self.client_modes.pop(client_id, None)
        self.video_tracks.pop(client_id, None)
        self.data_channels.pop(client_id, None)
        
        self.video_streaming.pop(client_id, None)
        self.gps_streaming.pop(client_id, None)
        if client_id in self.gps_tasks:
            task = self.gps_tasks[client_id]
            task.cancel()
            del self.gps_tasks[client_id]
        
        self.client_last_panorama_timestamp.pop(client_id, None)
        self.client_last_segmentation_timestamp.pop(client_id, None)
        self.client_last_gps_timestamp.pop(client_id, None)
        
        # Stop RTT monitoring if no clients remain
        if not self.peer_connections and self.rtt_monitoring_active:
            await self.stop_rtt_monitoring()
        
        client_logger.info(f"Client {client_id} disconnected and cleaned up")
    
    def get_streaming_stats(self):
        """Get streaming statistics"""
        return {
            'total_clients': len(self.peer_connections),
            'video_streaming': len(self.video_tracks),
            'gps_streaming': len([c for c in self.peer_connections.values() if c.connectionState == "connected"])
        }
    
    async def start_rtt_monitoring(self):
        """Start RTT monitoring for all connections"""
        if not self.rtt_monitoring_active:
            self.rtt_monitoring_active = True
            self.rtt_monitoring_task = asyncio.create_task(self._rtt_monitoring_loop())
            server_logger.info("RTT monitoring started")
    
    async def stop_rtt_monitoring(self):
        """Stop RTT monitoring"""
        if self.rtt_monitoring_active:
            self.rtt_monitoring_active = False
            if self.rtt_monitoring_task:
                self.rtt_monitoring_task.cancel()
                self.rtt_monitoring_task = None
            server_logger.info("RTT monitoring stopped")
    
    async def _rtt_monitoring_loop(self):
        """Main RTT monitoring loop - prints RTT stats every second"""
        while self.rtt_monitoring_active:
            try:
                await self._log_rtt_statistics()
                await asyncio.sleep(10.0)
            except asyncio.CancelledError:
                break
            except Exception as e:
                server_logger.error(f"Error in RTT monitoring loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _log_rtt_statistics(self):
        """Get and log RTT statistics for all active connections"""
        if not self.peer_connections:
            return
        
        current_time = datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        for client_id, pc in self.peer_connections.items():
            try:
                if pc.connectionState == "connected":
                    # Get WebRTC statistics
                    stats = await pc.getStats()
                    
                    # Extract RTT information from stats
                    rtt_info = self._extract_rtt_from_stats(stats)
                    
                    if rtt_info:
                        # Log RTT debug information
                        server_logger.info(
                            f"[RTT-DEBUG] {current_time} - Client {client_id}: "
                            f"RTT={rtt_info['rtt']:.1f}ms, "
                            f"Packets Sent={rtt_info.get('packets_sent', 'N/A')}, "
                            f"Packets Lost={rtt_info.get('packets_lost', 'N/A')}, "
                            f"Bytes Sent={rtt_info.get('bytes_sent', 'N/A')}"
                        )
                    else:
                        server_logger.warning(f"[RTT-DEBUG] {current_time} - Client {client_id}: RTT data not available")

            except Exception as e:
                server_logger.error(f"Error getting RTT stats for client {client_id}: {e}")
    
    def _extract_rtt_from_stats(self, stats):
        """Extract RTT information from WebRTC statistics"""
        try:
            rtt_info = {}
            
            for stat in stats.values():
                # Look for outbound RTP stream stats (contains RTT)
                if stat.type == "outbound-rtp":
                    if hasattr(stat, 'roundTripTime') and stat.roundTripTime is not None:
                        rtt_info['rtt'] = stat.roundTripTime * 1000  # Convert to milliseconds
                    if hasattr(stat, 'packetsSent'):
                        rtt_info['packets_sent'] = stat.packetsSent
                    if hasattr(stat, 'packetsLost'):
                        rtt_info['packets_lost'] = stat.packetsLost
                    if hasattr(stat, 'bytesSent'):
                        rtt_info['bytes_sent'] = stat.bytesSent
                
                # Also check remote-inbound-rtp stats for additional RTT data
                elif stat.type == "remote-inbound-rtp":
                    if hasattr(stat, 'roundTripTime') and stat.roundTripTime is not None:
                        rtt_info['rtt'] = stat.roundTripTime * 1000  # Convert to milliseconds
                
                # Check candidate-pair stats for RTT
                elif stat.type == "candidate-pair" and hasattr(stat, 'state') and stat.state == "succeeded":
                    if hasattr(stat, 'currentRoundTripTime') and stat.currentRoundTripTime is not None:
                        rtt_info['rtt'] = stat.currentRoundTripTime * 1000  # Convert to milliseconds
            
            return rtt_info if 'rtt' in rtt_info else None
            
        except Exception as e:
            server_logger.error(f"Error extracting RTT from stats: {e}")
            return None

# Create managers
zmq_data_manager = ZMQDataManager()
webrtc_manager = WebRTCManager(zmq_data_manager)

def get_local_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(("*******", 80))
    ip = s.getsockname()[0]
    s.close()
    return ip

async def webrtc_offer_handler(request):
    """Handle WebRTC offers and return answers"""
    try:
        data = await request.json()
        
        if data.get('type') != 'offer':
            return web.json_response({'error': 'Invalid request type'}, status=400)
            
        client_id = str(hash(data['sdp']))[0:8]
        
        pc = await webrtc_manager.create_peer_connection(client_id)
        
        # Set remote description
        offer = RTCSessionDescription(sdp=data['sdp'], type='offer')
        await pc.setRemoteDescription(offer)
        
        # Create answer
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)
        
        return web.json_response({
            'type': 'answer',
            'sdp': pc.localDescription.sdp
        })
        
    except Exception as e:
        if 'client_id' in locals():
            client_logger.error(f"WebRTC negotiation failed for {client_id}: {e}")
            await webrtc_manager.remove_client(client_id)
        else:
            server_logger.error(f"Error handling WebRTC offer: {e}")
        
        return web.json_response({'error': str(e)}, status=500)

async def get_json_file_list(request):
    """Return list of all JSON filenames in the data directory"""
    try:
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        json_files = glob.glob(os.path.join(data_dir, '*.json'))
        
        file_list = []
        for json_file in json_files:
            filename = os.path.basename(json_file)
            file_list.append(filename)
        
        return web.json_response({
            "status": "success",
            "count": len(file_list),
            "files": file_list
        })
        
    except Exception as e:
        server_logger.error(f"Error in get_json_file_list: {e}")
        return web.json_response({
            "status": "error",
            "message": str(e)
        }, status=500)

async def get_json_file_data(request):
    """Return specific JSON file content by filename"""
    try:
        filename = request.match_info['filename']
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        file_path = os.path.join(data_dir, filename)
        
        if not filename.endswith('.json'):
            return web.json_response({"status": "error", "message": "File must be a JSON file"}, status=400)
            
        if not os.path.exists(file_path):
            return web.json_response({"status": "error", "message": f"File {filename} not found"}, status=404)
            
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = json.load(f)
            return web.json_response({
                "status": "success",
                "filename": filename,
                "data": file_content
            })
            
    except json.JSONDecodeError as e:
        server_logger.error(f"Invalid JSON in {filename}: {e}")
        return web.json_response({"status": "error", "message": f"Invalid JSON format in {filename}"}, status=400)
    except Exception as e:
        server_logger.error(f"Error in get_json_file_data for {filename}: {e}")
        return web.json_response({"status": "error", "message": str(e)}, status=500)

async def init_app():
    """Initialize the web application"""
    app = web.Application()
    
    app.router.add_post('/webrtc/offer', webrtc_offer_handler)
    app.router.add_get('/data/files', get_json_file_list)
    app.router.add_get('/data/{filename}', get_json_file_data)
    
    await zmq_data_manager.setup_receivers()
    await zmq_data_manager.start_receivers()
    
    return app

async def main():
    """Main application entry point"""
    server_logger.info("Starting WebRTC VR360 Server...")
    
    app = await init_app()
    
    server_logger.info("Starting WebRTC server on port 6000...")
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 6000)
    await site.start()
    
    server_logger.info("  WebRTC VR360 Server started successfully!")
    server_logger.info("  WebRTC endpoint: http://0.0.0.0:6000/webrtc/offer")
    server_logger.info("  HTTP API: http://0.0.0.0:6000/data/")

    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        server_logger.info("Shutting down server...")
        
        # Stop RTT monitoring
        await webrtc_manager.stop_rtt_monitoring()
        
        # Close all peer connections
        for client_id in list(webrtc_manager.peer_connections.keys()):
            await webrtc_manager.remove_client(client_id)
        
        await zmq_data_manager.stop()
        await runner.cleanup()

if __name__ == '__main__':
    asyncio.run(main())
