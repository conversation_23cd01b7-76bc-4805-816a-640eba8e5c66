using System.Collections;
using System.Collections.Generic;
using Mapbox.Unity.Location;
using UnityEngine;
using UnityEngine.Assertions;

public class StreamLocationProvider : AbstractLocationProvider
{
    [SerializeField] LocationStream _locationStream;
    private void Awake() {
        Assert.IsNotNull(_locationStream);
        _locationStream.OnReceiveLocation.AddListener(ReceiveLocationHandler);
    }

    private void ReceiveLocationHandler(LocationInfo location) {
        _currentLocation = new Location
        {
            LatitudeLongitude = new Mapbox.Utils.Vector2d(location.latitude, location.longitude),
            UserHeading = location.heading,
        };
        SendLocation(_currentLocation);
    }

    private void OnDestroy() {
        _locationStream.OnReceiveLocation.RemoveListener(ReceiveLocationHandler);
    }
    
}
