using UnityEngine;
using System.Linq;

/// <summary>
/// Tools MonoBehaviour that provides tools for LLMClients.
/// Registers tools for POI import and display control.
/// </summary>
public class Tools : MonoBehaviour
{
    [Header("Pin Management")]
    [SerializeField] private PinManager pinManager;

    [<PERSON><PERSON>("LLM Client")]
    [SerializeField] private LLMClient llmClient;

    [<PERSON><PERSON>("Display Mode Control")]
    [SerializeField] private Manager manager;

    private void Start()
    {
        if (llmClient == null)
        {
            llmClient = FindObjectOfType<LLMClient>();
        }

        if (pinManager == null)
        {
            pinManager = FindObjectOfType<PinManager>();
        }

        if (manager == null)
        {
            manager = FindObjectOfType<Manager>();
        }

        RegisterTools();
    }

    private void RegisterTools()
    {
        if (llmClient == null)
        {
            Debug.LogError("No LLMClient found. Cannot register tools.");
            return;
        }

        // Tool 2: Control POI display
        var displayTool = new Tool
        {
            Name = "set_poi_display",
            Description = "Turn POI display on or off. The user may say '關閉 POI' to turn off the display, or '開啟 POI' to turn it on.",
            Example = "set_poi_display(true) or set_poi_display(false)",
            Executor = (args) => ControlPOIDisplay(args)
        };
        llmClient.RegisterTool(displayTool);

        // Tool 3: Control display mode
        var displayModeTool = new Tool
        {
            Name = "set_display_mode",
            Description = "Change the display mode. Only supports two modes: 'panorama' and 'segmentation'. The user may say '切換到全景模式' for panorama mode, or '切換到分割模式' for segmentation mode.",
            Example = "set_display_mode(panorama) or set_display_mode(segmentation)",
            Executor = (args) => ControlDisplayMode(args)
        };
        llmClient.RegisterTool(displayModeTool);

        Debug.Log("Tools registered successfully for LLM client.");
    }

    private void ControlPOIDisplay(string[] args)
    {
        string command = args[0].ToLower().Trim();

        switch (command)
        {
            case "true":
                Debug.Log("Turning POI display ON");
                pinManager.SetDisplay(true);
                break;

            case "false":
                Debug.Log("Turning POI display OFF");
                pinManager.SetDisplay(false);
                break;

            default:
                Debug.LogWarning($"Unknown POI display command: {command}. Use 'true' or 'false'.");
                pinManager.TogglePinDisplay();
                break;
        }
    }

    private void ControlDisplayMode(string[] args)
    {
        if (manager == null)
        {
            Debug.LogError("Manager not found. Cannot control display mode.");
            return;
        }

        if (args.Length == 0)
        {
            Debug.LogWarning("No display mode specified. Toggling display mode.");
            manager.ToggleDisplayMode();
            return;
        }

        string mode = args[0].ToLower().Trim();

        switch (mode)
        {
            case "panorama":
                Debug.Log("Setting display mode to Panorama");
                manager.SetDisplayMode(VideoMode.Panorama);
                break;

            case "segmentation":
                Debug.Log("Setting display mode to Segmentation");
                manager.SetDisplayMode(VideoMode.Segmentation);
                break;

            default:
                Debug.LogWarning($"Unknown display mode: {mode}. Use 'panorama' or 'segmentation'.");
                manager.ToggleDisplayMode();
                break;
        }
    }
}