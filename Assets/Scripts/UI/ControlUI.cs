using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;


public class ControlUI : MonoBehaviour
{
    public UnityEvent<string> OnPOIFileSelected;
    public GameObject ItemPrefab;
    public Transform ChoosePOICanva;
    public Transform ContentPanel;
    public Text StatusText;

    private void Awake()
    {
        if (ChoosePOICanva == null || ContentPanel == null || ItemPrefab == null)
        {
            Debug.LogError("ControlUI: Missing references in the inspector.");
        }
        ChoosePOICanva.gameObject.SetActive(false);
    }
    public async void ImportPOIs()
    {
        ChoosePOICanva.gameObject.SetActive(true);
        // Clear existing items
        foreach (Transform child in ContentPanel)
        {
            Destroy(child.gameObject);
        }

        // Import POIs from server
        var serverFileList = await PinImport.GetServerFileList();
        if (serverFileList == null || serverFileList.Count == 0)
        {
            Debug.LogWarning("No POI files found on the server.");
            return;
        }
        foreach (var fileName in serverFileList)
        {
            _createItem(fileName);
        }
    }
    private void _createItem(string fileName)
    {
        GameObject item = Instantiate(ItemPrefab, ContentPanel);
        item.GetComponentInChildren<Text>().text = fileName;

        Button button = item.GetComponentInChildren<Button>();
        button.onClick.AddListener(() =>
        {
            OnPOIFileSelected?.Invoke(fileName);
            ChoosePOICanva.gameObject.SetActive(false);
            StatusText.text = fileName;
        });
    }
}