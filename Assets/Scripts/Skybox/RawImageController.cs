using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

public class RawImageController : <PERSON>oB<PERSON><PERSON>our, ISkyboxController
{
    public RawImage image;
    private void Awake()
    {
        Assert.IsNotNull(image);
    }
    public void SetTexture(Texture texture)
    {
        if (texture == null)
        {
            Debug.LogError("CurvedCylinderController: The provided texture is null.");
            return;
        }

        image.texture = texture;
    }
}
