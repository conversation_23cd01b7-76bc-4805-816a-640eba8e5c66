using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// This class is responsible for controlling the skybox in the scene.
/// </summary>
public class SkyboxController : MonoBehaviour, ISkyboxController
{
    [SerializeField] Texture2D skyboxTexture;
    [SerializeField] Color skyboxColor = new(128, 128, 128, 128);
    [SerializeField] float skyboxRotation = 0;
    [SerializeField] float skyboxExposure = 1;
    // Start is called before the first frame update
    void Start()
    {
        SetSkyboxFromMaterial("Skybox");
        RenderSettings.skybox.SetTexture("_MainTex", null);
    }

    // Update is called once per frame
    void Update()
    {
       
    }
   
    /// <summary>
    /// Set the texture of the skybox.
    /// </summary>
    /// <param name="texture">The texture to set.</param>
    public void SetTexture(Texture texture)
    {
        if (texture == null)
        {
            Debug.LogError("SkyboxController: The provided texture is null.");
            return;
        }

        // Retrieve the current skybox material
        Material skyboxMaterial = RenderSettings.skybox;
        if (skyboxMaterial == null)
        {
            Debug.LogError("SkyboxController: No skybox material found in RenderSettings.");
            return;
        }

        // Check if the material has a "_MainTex" property to apply the texture
        if (skyboxMaterial.HasProperty("_MainTex"))
        {
            skyboxMaterial.SetTexture("_MainTex", texture);
        }
        else
        {
            Debug.LogError("SkyboxController: The skybox material does not have a '_MainTex' property.");
        }
    }

    /// <summary>
    /// Set the color of the skybox.
    /// </summary>
    /// <param name="color">The color to set.</param>
    public void SetColor(Color color)
    {
        Material skyboxMaterial = RenderSettings.skybox;
        if (skyboxMaterial == null)
        {
            Debug.LogError("SkyboxController: No skybox material found in RenderSettings.");
            return;
        }

        // Set the color of the skybox
        if (skyboxMaterial.HasProperty("_Tint"))
        {
            skyboxMaterial.SetColor("_Tint", color);
        }
        else
        {
            Debug.LogError("SkyboxController: The skybox material does not have a '_Tint' property.");
        }
    }

    /// <summary>
    /// Set the rotation of the skybox.
    /// </summary>
    /// <param name="rotation"></param>
    public void SetRotation(float rotation)
    {
        Material skyboxMaterial = RenderSettings.skybox;
        if (skyboxMaterial == null)
        {
            Debug.LogError("SkyboxController: No skybox material found in RenderSettings.");
            return;
        }

        // Set the rotation of the skybox
        if (skyboxMaterial.HasProperty("_Rotation"))
        {
            skyboxMaterial.SetFloat("_Rotation", rotation);
        }
        else
        {
            Debug.LogError("SkyboxController: The skybox material does not have a '_Rotation' property.");
        }
    }

    /// <summary>
    /// Set the exposure of the skybox.
    /// </summary>
    /// <param name="exposure"></param>
    public void SetExposure(float exposure)
    {
        Material skyboxMaterial = RenderSettings.skybox;
        if (skyboxMaterial == null)
        {
            Debug.LogError("SkyboxController: No skybox material found in RenderSettings.");
            return;
        }

        // Set the exposure of the skybox
        if (skyboxMaterial.HasProperty("_Exposure"))
        {
            skyboxMaterial.SetFloat("_Exposure", exposure);
        }
        else
        {
            Debug.LogError("SkyboxController: The skybox material does not have a '_Exposure' property.");
        }
    }

    public void SetSkyboxFromMaterial(string materialPath)
    {
        if (string.IsNullOrEmpty(materialPath))
        {
            Debug.LogError("SkyboxMaterialLoader: The material path is null or empty.");
            return;
        }

        // need to delete "Assets/" & ".mat" before call Resources.Load
        Material skyboxMaterial = Resources.Load<Material>(materialPath);

        if (skyboxMaterial == null)
        {
            Debug.LogError($"SkyboxMaterialLoader: Failed to load material at path {materialPath}. Ensure the material is in a 'Resources' folder.");
            return;
        }

        RenderSettings.skybox = skyboxMaterial;
        Debug.Log($"Skybox material updated successfully from path: {materialPath}");
    }
}
