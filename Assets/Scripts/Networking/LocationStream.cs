using UnityEngine;
using UnityEngine.Events;
using System;
using Newtonsoft.Json;

[Serializable]
public struct LocationInfo
{
    public double latitude;
    public double longitude;
    public double altitude;
    public int heading;
}

[RequireComponent(typeof(WebRTCClient))]
public class LocationStream : MonoBehaviour
{
    [Header("Events")]
    public UnityEvent<LocationInfo> OnReceiveLocation = new();

    private WebRTCClient webrtcClient;
    
    private void Start()
    {
        webrtcClient = GetComponent<WebRTCClient>();
        webrtcClient.AddEventListener("gps", HandleLocationEvent);
        webrtcClient.OnDataChannelOpen.AddListener(() => {
            webrtcClient.SendDataChannelMessage("start_gps");
            Debug.Log("GPS data channel opened, started receiving GPS data.");
        });
    }

    private void OnDestroy()
    {
        if (webrtcClient != null)
        {
            webrtcClient.RemoveEventListener("gps", HandleLocationEvent);
        }
    }

    private void HandleLocationEvent(string data)
    {
        try
        {
            var json = data.Replace("\\", ""); // 防 escape
            LocationInfo info = JsonConvert.DeserializeObject<LocationInfo>(json);
            OnReceiveLocation?.Invoke(info);
        }
        catch (Exception e)
        {
            Debug.LogError($"Error handling location event: {e.Message}");
            Debug.LogError($"Raw data: {data}");
        }
    }
}
