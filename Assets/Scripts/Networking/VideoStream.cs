﻿using UnityEngine;
using UnityEngine.Events;
using System;

public enum VideoMode
{
    Panorama,
    Segmentation
}

[RequireComponent(typeof(WebRTCClient))]
public class VideoStream : MonoBehaviour
{
    [Header("Video Settings")]
    public VideoMode mode = VideoMode.Panorama;
    
    [Header("Events")]
    public UnityEvent<Texture> OnReceiveVideo = new UnityEvent<Texture>();
    
    private WebRTCClient webrtcClient;
    
    /// <summary>
    /// Convert VideoMode enum to string for server communication
    /// </summary>
    private string GetModeString(VideoMode videoMode)
    {
        return videoMode switch
        {
            VideoMode.Panorama => "panorama",
            VideoMode.Segmentation => "segmentation",
            _ => "panorama"
        };
    }
    
    /// <summary>
    /// Convert string to VideoMode enum
    /// </summary>
    private VideoMode GetModeFromString(string modeString)
    {
        return modeString?.ToLower() switch
        {
            "segmentation" => VideoMode.Segmentation,
            "panorama" or _ => VideoMode.Panorama
        };
    }
    
    private void Start()
    {
        webrtcClient = GetComponent<WebRTCClient>();
        webrtcClient.OnVideoReceived.AddListener(HandleVideoReceived);
        webrtcClient.OnDataChannelOpen.AddListener(() =>
        {
            webrtcClient.SendDataChannelMessage("start_video", GetModeString(mode));
        });
    }

    private void OnDestroy()
    {
        if (webrtcClient != null)
        {
            webrtcClient.OnVideoReceived.RemoveListener(HandleVideoReceived);
        }
    }

    private void HandleVideoReceived(Texture texture)
    {
        OnReceiveVideo?.Invoke(texture);
    }
    
    /// <summary>
    /// Change the video mode (panorama/segmentation)
    /// </summary>
    public void SetMode(VideoMode newMode)
    {
        mode = newMode;
        if (webrtcClient != null)
        {
            webrtcClient.SendDataChannelMessage("set_mode", GetModeString(mode));
            Debug.Log($"Video mode changed to: {GetModeString(mode)}");
        }
    }
    
    /// <summary>
    /// Change the video mode using string (for backward compatibility)
    /// </summary>
    public void SetMode(string newMode)
    {
        SetMode(GetModeFromString(newMode));
    }
}
