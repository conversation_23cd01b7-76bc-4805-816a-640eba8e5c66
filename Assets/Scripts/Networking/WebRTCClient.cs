using UnityEngine;
using Unity.WebRTC;
using UnityEngine.Networking;
using System.Text;
using UnityEngine.Events;
using System.Collections.Generic;
using System;
using System.Collections;
using Cysharp.Threading.Tasks;
using System.Text.RegularExpressions;

public class WebRTCClient : MonoBehaviour
{
    [Header("WebRTC Settings")]
    public string serverUrl = "http://localhost:6000";
    
    [Header("Events")]
    public UnityEvent OnConnected = new();
    public UnityEvent OnDataChannelOpen = new();
    public UnityEvent OnDisconnected = new();
    public UnityEvent<Texture> OnVideoReceived = new();
    public UnityEvent<string> OnDataReceived = new();
    
    private RTCPeerConnection peerConnection;
    private RTCDataChannel dataChannel;
    
    private bool isConnected = false;
    private Dictionary<string, List<UnityAction<string>>> eventListeners = new Dictionary<string, List<UnityAction<string>>>();
    
    public bool IsConnected => isConnected;
    
    void Start()
    {
        StartCoroutine(WebRTC.Update());
    }
    
    void OnDestroy()
    {
        Disconnect();
    }
    
    public async void Connect()
    {
        if (isConnected) return;
        
        Debug.Log($"Connecting to WebRTC server: {serverUrl}");
        await StartWebRTCConnection();
    }
    
    public void Disconnect()
    {
        isConnected = false;
        
        if (dataChannel != null)
        {
            dataChannel.Close();
            dataChannel = null;
        }
        
        if (peerConnection != null)
        {
            peerConnection.Close();
            peerConnection = null;
        }
        
        OnDisconnected?.Invoke();
    }
    
    private async UniTask StartWebRTCConnection()
    {
        var config = new RTCConfiguration
        {
            iceServers = new RTCIceServer[]
            {
                new RTCIceServer { urls = new string[] { "stun:stun.l.google.com:19302" } }
            }
        };
        
        peerConnection = new RTCPeerConnection(ref config);
        
        peerConnection.OnIceCandidate = OnIceCandidate;
        peerConnection.OnIceConnectionChange = OnIceConnectionChange;
        peerConnection.OnTrack = OnTrack;
        peerConnection.OnDataChannel = OnDataChannel;
        
        dataChannel = peerConnection.CreateDataChannel("data");
        
        dataChannel.OnOpen = () =>
        {
            Debug.Log("Data channel opened on client side");
            OnDataChannelOpen?.Invoke();
        };
        dataChannel.OnClose = () => Debug.Log("Data channel closed on client side");
        dataChannel.OnMessage = OnDataChannelMessage;
        peerConnection.AddTransceiver(TrackKind.Video, new RTCRtpTransceiverInit
        {
            direction = RTCRtpTransceiverDirection.RecvOnly
        });
        
        await CreateOfferAndConnect();
    }
    private async UniTask CreateOfferAndConnect()
    {
        var op = peerConnection.CreateOffer();
        await op.ToUniTask();
        
        if (op.IsError)
        {
            Debug.LogError($"Failed to create offer: {op.Error}");
            return;
        }

        var offer = op.Desc;
        
        var setLocalDesc = peerConnection.SetLocalDescription(ref offer);
        await setLocalDesc.ToUniTask();
        
        if (setLocalDesc.IsError)
        {
            Debug.LogError($"Failed to set local description: {setLocalDesc.Error}");
            return;
        }

        await SendOfferGetAnswer(offer.sdp);
    }
    
    private async UniTask SendOfferGetAnswer(string offerSdp)
    {
        var offerData = new OfferRequest
        {
            type = "offer",
            sdp = offerSdp
        };
        
        string jsonData = JsonUtility.ToJson(offerData);
        byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
        
        using (UnityWebRequest request = new UnityWebRequest($"{serverUrl}/webrtc/offer", "POST"))
        {
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            
            await request.SendWebRequest().ToUniTask();
            
            if (request.result == UnityWebRequest.Result.Success)
            {
                var response = JsonUtility.FromJson<AnswerResponse>(request.downloadHandler.text);
                await HandleAnswer(response.sdp);
            }
            else
            {
                Debug.LogError($"HTTP request failed: {request.error}");
                Debug.LogError($"HTTP response: {request.downloadHandler.text}");
            }
        }
    }
    
    private async UniTask HandleAnswer(string answerSdp)
    {
        var answer = new RTCSessionDescription
        {
            type = RTCSdpType.Answer,
            sdp = answerSdp
        };
        
        var op = peerConnection.SetRemoteDescription(ref answer);
        await op.ToUniTask();
        
        if (op.IsError)
        {
            Debug.LogError($"Failed to set remote description: {op.Error}");
        }
    }
    
    private void OnIceCandidate(RTCIceCandidate candidate)
    {
        // ICE candidates are handled within SDP in HTTP mode
    }
    
    private void OnIceConnectionChange(RTCIceConnectionState state)
    {
        if (state == RTCIceConnectionState.Connected)
        {
            isConnected = true;
            OnConnected?.Invoke();
        }
        else if (state == RTCIceConnectionState.Closed || state == RTCIceConnectionState.Failed)
        {
            isConnected = false;
            OnDisconnected?.Invoke();
        }
    }
    
    private void OnTrack(RTCTrackEvent e)
    {
        if (e.Track is VideoStreamTrack videoTrack)
        {
            videoTrack.OnVideoReceived += OnVideoReceived.Invoke;
        }
    }
    
    private void OnDataChannel(RTCDataChannel channel)
    {
        dataChannel = channel;
        dataChannel.OnMessage = OnDataChannelMessage;
        dataChannel.OnOpen = () => Debug.Log("Data channel opened");
        dataChannel.OnClose = () => Debug.Log("Data channel closed");
    }
    
    private void OnDataChannelMessage(byte[] data)
    {
        string message = Encoding.UTF8.GetString(data);
        OnDataReceived?.Invoke(message);
        HandleDataChannelMessage(message);
    }
    
    private void HandleDataChannelMessage(string message)
    {
        try
        {
            string pattern = @"^\[\s*([^,\]]+)\s*,\s*(.*?)\s*\]$";
            Match match = Regex.Match(message.Trim(), pattern);
            
            if (match.Success)
            {
                string topic = match.Groups[1].Value.Trim();
                string content = match.Groups[2].Value.Trim();

                if (eventListeners.TryGetValue(topic, out List<UnityAction<string>> callbacks))
                {
                    foreach (var callback in callbacks)
                    {
                        callback.Invoke(content);
                    }
                }
            }
            else
            {
                Debug.LogWarning($"Invalid message format. Expected [topic, content], got: {message}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to handle data channel message: {e.Message}");
        }
    }
    
    public void SendDataChannelMessage(string type, string data = null)
    {
        if (dataChannel != null && dataChannel.ReadyState == RTCDataChannelState.Open)
        {
            string message = $"[{type}, {data ?? ""}]";
            dataChannel.Send(message);
        }
        else
        {
            Debug.LogWarning("Data channel is not open, cannot send message");
        }
    }
    
    public void AddEventListener(string eventName, UnityAction<string> callback)
    {
        if (!eventListeners.ContainsKey(eventName))
        {
            eventListeners.Add(eventName, new List<UnityAction<string>>());
        }
        eventListeners[eventName].Add(callback);
    }
    
    public void RemoveEventListener(string eventName, UnityAction<string> callback)
    {
        if (eventListeners.TryGetValue(eventName, out List<UnityAction<string>> callbacks))
        {
            callbacks.Remove(callback);
        }
    }
    
    [System.Serializable]
    private class OfferRequest
    {
        public string type;
        public string sdp;
    }
    
    [System.Serializable]
    private class AnswerResponse
    {
        public string type;
        public string sdp;
    }
}
