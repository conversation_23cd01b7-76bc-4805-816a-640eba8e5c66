using UnityEngine;
using UnityEngine.Assertions;

public class Actioner : MonoBeh<PERSON>our
{
    [SerializeField] private VoiceController voiceController;
    [SerializeField] private PinManager pinManager;

    [SerializeField] private Manager manager;

    private void Start()
    {
        Assert.IsNotNull(voiceController);
        Assert.IsNotNull(pinManager);
        Assert.IsNotNull(manager);
        voiceController.OnFullTranscription.AddListener(HandleTranscription);
    }

    private void HandleTranscription(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            Debug.LogWarning("Received empty transcription.");
            return;
        }
        Debug.Log($"Transcription: {text}");

        if (text.Contains("全景", System.StringComparison.OrdinalIgnoreCase))
        {
            manager.SetDisplayMode(VideoMode.Panorama);
        }
        else if (text.Contains("分割", System.StringComparison.OrdinalIgnoreCase))
        {
            manager.SetDisplayMode(VideoMode.Segmentation);
        }

        if (text.Contains("顯示", System.StringComparison.OrdinalIgnoreCase))
        {
            pinManager.SetDisplay(true);
        }
        else if (text.Contains("關閉", System.StringComparison.OrdinalIgnoreCase))
        {
            pinManager.SetDisplay(false);
        }
    }
}