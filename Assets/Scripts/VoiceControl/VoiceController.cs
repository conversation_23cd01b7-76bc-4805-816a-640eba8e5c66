using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;
using Meta.WitAi.Dictation;

public class VoiceController: MonoBehaviour{
    [SerializeField] private DictationService dictationService;
    [SerializeField] private AudioButton audioButton;
    public UnityEvent<string> OnFullTranscription = new();

    void Awake()
    {
        audioButton.OnAudioToggle.AddListener((isEnabled) => {
            if (isEnabled)
            {
                dictationService.Activate();
            }
            else
            {
                dictationService.Cancel();
            }
        });
    }

    void OnEnable()
    {
        dictationService.DictationEvents.OnFullTranscription.AddListener(OnFullTranscription.Invoke);
        dictationService.DictationEvents.OnStoppedListening.AddListener(StopListening);
    }
    void OnDisable()
    {
        dictationService.DictationEvents.OnFullTranscription.RemoveListener(OnFullTranscription.Invoke);
        dictationService.DictationEvents.OnStoppedListening.RemoveListener(StopListening);
    }

    private void StopListening()
    {
        audioButton.SetAudioActive(false);
    }
}