using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Mapbox.Utils;
using Mapbox.Json;
using UnityEngine;
using UnityEngine.Networking;

public static class PinImport
{
    // Server file data structure
    [System.Serializable]
    public class ServerFileData
    {
        public string fileName;
        public List<PinData> content;
    }

    // Server response data structure for file list
    [System.Serializable]
    private class FileListResponse
    {
        public string status;
        public int count;
        public string[] files;
    }

    // Server response data structure for single file data
    [System.Serializable]
    private class FileDataResponse
    {
        public string status;
        public string filename;
        public EsriFeatureCollection data;
    }

    public static string serverUrl = "http://localhost:6000";
    
    public static async UniTask<List<PinData>> ImportFromServer(string fileName)
    {
        var pinDataList = new List<PinData>();
        
        try
        {
            string fileUrl = $"{serverUrl}/data/{fileName}";
            Debug.Log($"Importing from server: {fileUrl}");
            
            using (UnityWebRequest request = UnityWebRequest.Get(fileUrl))
            {
                var operation = request.SendWebRequest();

                while (!operation.isDone)
                {
                    await UniTask.Yield();
                }

                if (request.result == UnityWebRequest.Result.Success)
                {
                    string jsonResponse = request.downloadHandler.text;
                    FileDataResponse response = JsonConvert.DeserializeObject<FileDataResponse>(jsonResponse);

                    if (response.status == "success" && response.data.features != null)
                    {
                        foreach (var feature in response.data.features)
                        {
                            // Convert TWD97 coordinates to WGS84 lat/lon
                            var (longitude, latitude) = CoordinateConverter.ConvertTWD97ToWGS84(feature.geometry.x, feature.geometry.y);

                            pinDataList.Add(new PinData
                            {
                                location = new Vector2d(latitude, longitude),
                                name1 = feature.attributes.MARKNAME1 ?? "",
                                name2 = feature.attributes.MARKNAME2 ?? "",
                                id = System.Guid.NewGuid()
                            });
                        }

                        Debug.Log($"Successfully imported {pinDataList.Count} pins from {fileName}");
                    }
                    else
                    {
                        Debug.LogError($"Server response error for {fileName}: {response.status}");
                    }
                }
                else
                {
                    Debug.LogError($"Failed to get file {fileName}: {request.error}");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error importing {fileName} from server: {e.Message}");
        }
        
        return pinDataList;
    }
    
    public static async UniTask<List<string>> GetServerFileList()
    {
        var fileList = new List<string>();
        
        try
        {
            string listUrl = $"{serverUrl}/data/files";
            Debug.Log($"Getting file list from server: {listUrl}");
            
            using (UnityWebRequest request = UnityWebRequest.Get(listUrl))
            {
                var operation = request.SendWebRequest();

                while (!operation.isDone)
                {
                    await UniTask.Yield();
                }

                if (request.result == UnityWebRequest.Result.Success)
                {
                    string jsonResponse = request.downloadHandler.text;
                    FileListResponse response = JsonConvert.DeserializeObject<FileListResponse>(jsonResponse);

                    if (response.status == "success" && response.files != null)
                    {
                        fileList.AddRange(response.files);
                        Debug.Log($"Successfully retrieved {response.count} file names from server");
                    }
                    else
                    {
                        Debug.LogError($"Server response error: {response.status}");
                    }
                }
                else
                {
                    Debug.LogError($"Failed to get file list: {request.error}");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error getting file list from server: {e.Message}");
        }
        
        return fileList;
    }
    public static async UniTask<List<PinData>> ImportFromJsonFile()
    {
        var path = await PickFile("json");
        if (path == null)
            return new List<PinData>();

        var json = System.IO.File.ReadAllText(path);
        EsriFeatureCollection result = JsonConvert.DeserializeObject<EsriFeatureCollection>(json);
        
        List<PinData> pinDataList = new List<PinData>();
        
        foreach (var feature in result.features)
        {
            // Convert TWD97 coordinates to WGS84 lat/lon
            var (longitude, latitude) = CoordinateConverter.ConvertTWD97ToWGS84(feature.geometry.x, feature.geometry.y);
            
            pinDataList.Add(new PinData { 
                location = new Vector2d(latitude, longitude), 
                name1 = feature.attributes.MARKNAME1 ?? "", 
                name2 = feature.attributes.MARKNAME2 ?? "", 
                id = System.Guid.NewGuid() 
            });
        }
        
        return pinDataList;
    }

    private static async UniTask<string> PickFile(string fileType)
    {
        if (NativeFilePicker.IsFilePickerBusy())
            return null;

        string _fileType = NativeFilePicker.ConvertExtensionToFileType(fileType);
        string pickedPath = null;

        var permission = NativeFilePicker.PickFile((path) => {
            pickedPath = path;
        }, new string[] { _fileType });

        while (NativeFilePicker.IsFilePickerBusy())
            await UniTask.Yield();

        return pickedPath;
    }

    private struct EsriFeatureCollection 
    {
        public EsriFeature[] features;
    }

    private struct EsriFeature 
    {
        public EsriAttributes attributes;
        public EsriGeometry geometry;
    }

    private struct EsriAttributes 
    {
        public string MARKNAME1;
        public string MARKNAME2;
    }

    private struct EsriGeometry 
    {
        public double x;
        public double y;
    }
}