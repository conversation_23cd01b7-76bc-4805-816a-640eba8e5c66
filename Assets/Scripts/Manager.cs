using UnityEngine;
using UnityEngine.Assertions;

public class Manager : MonoBehaviour
{
    public RawImageController skyboxController;
    public RawImageController segmentationImageController;
    public VideoStream videoStream;
    public WebRTCClient client;

    private void Awake()
    {
        Assert.IsNotNull(skyboxController);
        Assert.IsNotNull(segmentationImageController);
        Assert.IsNotNull(videoStream);
        Assert.IsNotNull(client);
    }

    private void Start()
    {
        skyboxController.image.enabled = true;
        segmentationImageController.image.enabled = false;
        videoStream.OnReceiveVideo.RemoveListener(segmentationImageController.SetTexture);
        videoStream.OnReceiveVideo.AddListener(skyboxController.SetTexture);
    }

    public void ToggleDisplayMode()
    {
        if (videoStream.mode == VideoMode.Panorama)
        {
            SetDisplayMode(VideoMode.Segmentation);
        }
        else
        {
            SetDisplayMode(VideoMode.Panorama);
        }
    }

    public void SetDisplayMode(VideoMode mode)
    {
        videoStream.SetMode(mode);
        switch (videoStream.mode)
        {
            case VideoMode.Panorama:
                skyboxController.image.enabled = true;
                segmentationImageController.image.enabled = false;
                videoStream.OnReceiveVideo.RemoveListener(segmentationImageController.SetTexture);
                videoStream.OnReceiveVideo.AddListener(skyboxController.SetTexture);
                break;
            case VideoMode.Segmentation:
                skyboxController.image.enabled = false;
                segmentationImageController.image.enabled = true;
                videoStream.OnReceiveVideo.RemoveListener(skyboxController.SetTexture);
                videoStream.OnReceiveVideo.AddListener(segmentationImageController.SetTexture);
                break;
        }
    }

    
}