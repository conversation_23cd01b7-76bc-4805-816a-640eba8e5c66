using System.Collections;
using System.Collections.Generic;
using System.Net.WebSockets;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Assertions;

public class ConnectionManager : MonoBehaviour
{
    public UDPBroadcastClient udpBroadcastClient;
    public WebRTCClient client;
    private static string hostIpPort = null;

    void Awake()
    {
        Assert.IsNotNull(udpBroadcastClient, "UDPBroadcastClient is null");
        Assert.IsNotNull(client, "WebRTC Client is null");
    }

    void StartConnection(string ipPort)
    {
        string httpUrl = $"http://{ipPort}";
        client.serverUrl = httpUrl;
        
        Debug.Log($"Setting WebRTC server URL to: {httpUrl}");
        
        client.OnConnected.AddListener(() => {
            Debug.Log("WebRTC client connected successfully");
        });
        
        client.OnDisconnected.AddListener(() => {
            Debug.Log("WebRTC client disconnected");
        });
        
        client.Connect();
    }

    async void Start()
    {
        if (hostIpPort == null)
        {
            Debug.Log("Starting server discovery...");
            int maxAttempts = 3;
            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                Debug.Log($"Server discovery attempt {attempt}/{maxAttempts}");
                hostIpPort = await udpBroadcastClient.DiscoverServer();

                if (hostIpPort != null)
                {
                    Debug.Log($"Server discovered: {hostIpPort}");
                    break;
                }

                if (attempt < maxAttempts)
                {
                    Debug.Log("Server not found, retrying in 500ms...");
                    await UniTask.Delay(500);
                }
            }

            if (hostIpPort == null)
            {
                Debug.LogError($"Failed to discover server after {maxAttempts} attempts");
                return;
            }
        }
        else
        {
            Debug.Log($"Using cached server address: {hostIpPort}");
        }
        
        StartConnection(hostIpPort);
        PinImport.serverUrl = $"http://{hostIpPort}";
    }
    

    void OnDestroy()
    {
        client.Disconnect();
    }
}
